import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class GroupRoutes extends React.Component {
    render() {
        const { url } = this.props.match;
        return (
            <Switch>
                <Route
                    exact
                    path={`${url}/edit/:id`}
                    component={asyncComponent(() =>
                        import("../../components/group/editGroup")
                    )}
                />
                <Route
                    exact
                    path={`${url}/view/:id`}
                    component={asyncComponent(() =>
                        import("../../components/group/viewGroup")
                    )}
                />
                <Route
                    exact
                    path={`${url}/view/:id/add-user`}
                    component={asyncComponent(() =>
                        import("../../components/group/addGroupUser")
                    )}
                />
                <Route
                    exact
                    path={`${url}/add`}
                    component={asyncComponent(() =>
                        import("../../components/group/addGroup")
                    )}
                />
                <Route
                    exact
                    path={`${url}`}
                    component={asyncComponent(() =>
                        import("../../components/group/groupManagement")
                    )}
                />
            </Switch>
        );
    }
}

export default GroupRoutes;

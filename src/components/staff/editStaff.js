import { Button, Form, Icon, Input, message, Modal, Tag, Select, Spin, Upload, Checkbox } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditStaff extends React.Component {
	state = {
		staffData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		companyData: [],
		isModalVisible: false,
		newPassword: "",
		isAdmin: false,
		staffId: "",
		editId: "",
		editShipmentTypeCheckbox: false

	};

	componentDidMount() {
		const id = this.props.match.params.id;
		const editId = this.props.match.params.id;
		this.props.changeCurrent("staff");
		const userType = localStorage.getItem("userType") === "1" ? true : false;
		const staffId = localStorage.getItem("staffId");
		this.setState({ contentloader: true, isAdmin: userType, staffId: staffId, editId: editId });

		const data1 = [];
		API.post("api/admin/staff/view-company-list", data1)
			.then((response) => {
				if (response) {
					this.setState({
						companyData: response.data && response.data,
						contentloader: false,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		const data = [];
		data["data"] = { staff_id: id };
		API.post("api/admin/staff/view-staff", data)
			.then((response) => {
				if (response) {
					this.setState(
						{
							staffData: response.data && response.data,
							storage_staff_id: response.data && response.data.storage_staff_id,
							warehouse_id: response.data && response.data.warehouse_id,
							fileList: response.data && response.data.staff_profile !== "" && response.data.staff_profile !== undefined && response.data.staff_profile !== null ? [
								{
									uid: -1,
									status: "done",
									url: response.data.staff_profile,
								},
							]
								: [],
							previewImage: response.data.staff_profile,
							previewVisible: true,
							contentloader: false,
							editShipmentTypeCheckbox: response.data && response.data.roles == "ADMIN" ? true : false,
						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};

	handleUpload = ({ fileList }) => {
		this.setState({ fileList });
	};

	isCompanyExist = (id, companyData) => {
		let status = false;
		for (let i = 0; i < companyData.length; i++) {
			let company_id = companyData[i].company_id;
			if (company_id === id) {
				status = true;
				break;
			}
		}
		return status;
	};


	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				const tag = [];
				if (values.tags) {
					values.tags.forEach((item) => {
						tag.push(item.key);
					});
				}
				formData.append("staff_id", this.props.match.params.id);
				formData.append("company_id", values.company.key ? values.company.key : "");
				formData.append("first_name", values.first_name);
				formData.append("last_name", values.last_name);
				formData.append("notes", values.notes);
				formData.append("phone", values.phone);
				formData.append("country_code", values.country_code);
				formData.append("email", values.email);
				formData.append("roles", values.roles);
				formData.append("is_admin_can_edit_shipment_type", (values.roles == "ADMIN") ? values.is_admin_can_edit_shipment_type : false);
				formData.append(
					"photo",
					values.profile && values.profile !== "" && this.state.fileList.length > 0
						? this.state.fileList[0].originFileObj
						: ""
				);
				formData.append("tag", JSON.stringify(tag));
				data["data"] = formData;

				this.setState({ loading: true, contentloader: true });
				API.post("api/admin/staff/edit-staff", data)
					.then((response) => {
						if (response) {
							if (this.props.match.params.id == this.state.staffId) {
								localStorage.setItem("editShipmentTypeFlagCheck", values.is_admin_can_edit_shipment_type == true ? 1 : 0);
							}
							this.setState({ loading: false, contentloader: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false, contentloader: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, contentloader: false });
					});
			}
		});
	};

	showModal = () => {
		this.setState({ isModalVisible: true });
	};

	onPasswordChange = (e) => {
		this.setState({ newPassword: e.target.value });
	};

	handlePasswordChange = (e) => {
		e.preventDefault();
		let formData = new FormData();
		const data = [],
			id = this.props.match.params.id;
		formData.append("staff_id", id);
		formData.append("password", this.state.newPassword);
		data["data"] = formData;
		this.setState({ loading: true });
		API.post("api/admin/staff/update-password", data)
			.then((response) => {
				if (response) {
					this.setState({ loading: false });
					message.success(response.message);
					this.setState({ isModalVisible: false });
				} else {
					this.setState({ loading: false });
					message.error(response.message);
					this.setState({ isModalVisible: false });
				}
			})
			.catch((error) => {
				this.setState({ loading: false });
			});
	};

	handleCancel = () => {
		this.setState({ isModalVisible: false });
	};

	OnChangeSelectUser = (value, option) => {
		if (value == "ADMIN") {
			this.setState({
				editShipmentTypeCheckbox: true
			})
		}
		else {
			this.setState({
				editShipmentTypeCheckbox: false
			})
		}
	}

	render() {


		const { getFieldDecorator } = this.props.form;
		const { staffData, companyData, isModalVisible, isAdmin } = this.state;

		const initialCompany =
			(staffData && staffData.company_id === "") ||
				(staffData && staffData.company_id && !this.isCompanyExist(staffData.company_id, companyData))
				? {}
				: { key: staffData && staffData.company_id };

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: staffData && staffData.country_code,
		})(
			<Select disabled style={{ width: 70 }}>
				<Option value='1'>+1</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit User
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company name'>
									{getFieldDecorator("company", {
										initialValue: initialCompany,
										rules: [
											{
												required: true,
												message: "Please input company name!",
											},
										],
									})(
										<Select
											disabled={!isAdmin}
											size={"default"}
											labelInValue
											placeholder='Select Company'
											style={{ width: "100%" }}>
											{companyData
												? companyData.map((e) => (
													<Option key={e.company_id} value={e.company_id}>
														{e.company_name}
													</Option>
												))
												: null}
										</Select>
									)}
								</Form.Item>
								<Form.Item label='Role'>
									{getFieldDecorator("roles", {
										initialValue: staffData && staffData.roles,
										rules: [{ required: true, message: "Please select your role!" }],
									})(
										<Select
											placeholder='Select role'
											onChange={this.OnChangeSelectUser}
											disabled={
												(staffData &&
													this.props.location.state.isSingleAdmin === "true" &&
													staffData.roles !== "WORKER") ||
												this.state.editId === this.state.staffId || this.props.location.state.chackAdminLast
											}>
											<Option value='ADMIN'>Admin</Option>
											<Option value='WORKER'>Worker</Option>
										</Select>
									)}
								</Form.Item>

								{this.state.editShipmentTypeCheckbox ?
									<Form.Item label='Edit Access for Shipment Type'>
										{getFieldDecorator(`is_admin_can_edit_shipment_type`, {
											valuePropName: "checked",
											initialValue: staffData && staffData.is_admin_can_edit_shipment_type,

										})(
											<Checkbox disabled={staffData.email == localStorage.getItem("companyEmailForStaff")} >Edit Access for Shipment Type</Checkbox>)}
									</Form.Item>
									: ""
								}

								<Form.Item label='First Name'>
									{getFieldDecorator("first_name", {
										initialValue: staffData && staffData.first_name,
										placeholder: "First Name",
										rules: [
											{
												required: true,
												message: "Please input first name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='First Name' />)}
								</Form.Item>
								<Form.Item label='Last Name'>
									{getFieldDecorator("last_name", {
										initialValue: staffData && staffData.last_name,
										placeholder: "Last Name",
										rules: [
											{
												required: true,
												message: "Please input last name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Last Name' />)}
								</Form.Item>
								<Form.Item label='Email'>
									{getFieldDecorator("email", {
										initialValue: staffData && staffData.email,
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input disabled={this.state.editId === this.state.staffId} />)}
								</Form.Item>
								<Form.Item label='Phone Number'>
									{getFieldDecorator("phone", {
										initialValue: staffData && staffData.phone,
									})(
										<Input
											style={{ width: "100%" }}
											placeholder='Phone Number'
											customInput={Input}
											maxLength={20}
										/>
									)}
								</Form.Item>
								<Form.Item label='Password'>
									<Button type='primary' onClick={this.showModal}>
										Change Password
									</Button>
								</Form.Item>
								<Modal
									title='Change Password'
									visible={isModalVisible}
									onCancel={this.handleCancel}
									footer={[
										<Button key='submit' onClick={this.handleCancel}>
											Cancel
										</Button>,
										<Button
											key='link'
											type='primary'
											loading={this.state.loading}
											onClick={this.handlePasswordChange}>
											Change Password
										</Button>,
									]}>
									<Form.Item label='Password'>
										{getFieldDecorator("password", {
											rules: [
												{
													min: 8,
													message:
														"Password must be minimum 8 characters with one lowerCase, one upperCase and one special symbol!!",
												},
											],
										})(
											<Input
												placeholder='Enter New Password'
												name='password'
												onChange={this.onPasswordChange}
											/>
										)}
									</Form.Item>
								</Modal>
								<Form.Item label='User Notes'>
									{getFieldDecorator("notes", {
										initialValue: staffData && staffData.notes,
									})(<TextArea rows={4} placeholder='User Notes' />)}
								</Form.Item>
								<Form.Item label='Profile Image'>
									{getFieldDecorator("profile")(
										<Upload
											listType='picture-card'
											fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											showUploadList={{
												showRemoveIcon: true,
												showPreviewIcon: false,
											}}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											{this.state.fileList && this.state.fileList.length < 1 && (
												<Button>
													<Icon type='upload' /> Click to Upload
												</Button>
											)}
										</Upload>

									)}
								</Form.Item>
								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditStaff));

import { Button, Col, Icon, Input, message, Modal, Row, Spin, Table, Tooltip, Checkbox } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;

export default class roomListManagement extends React.Component {
	state = {
		apiParam: {},
		roomList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		adminId: "",
		companyId: "",
		staffId: "",
		batchAction: [],
		groupUserId: "",

	};
	componentDidMount() {
		let params = {
			search: "",
			order_by_fields: "",
			page_no: "1",
			order_sequence: "",
			page_size: 25,
			filter: "active",
		};
		this.setState({ apiParam: params }, () => this.fetchItemList());

		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});
	}
	fetchItemList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		const {
			search,
			page_no,
			order_sequence,
			page_size,
			order_by_fields,
			filter,
		} = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/group/user/list/${this.props.match.params.id}?order_by_fields=${order_by_fields}&order_sequence=${order_sequence}&page_no=${page_no}&search=${search}&page_size=${page_size}&filter=${filter}`
		)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.count;
						this.setState({
							roomList: response.data.rows,
							pagination,
							pageloading: false,
							confirmLoading: false,
							pageloading: false,
						});

					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
							confirmLoading: false,
							pageloading: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		let params = {
			...this.state.apiParam,
			page_no: pagination.current,
			order_by_fields: sorter.column && sorter.field ? sorter.field : "",
			order_sequence: sorter.order === "ascend" ? "ASC" : "DESC",
			page_size: pagination.pageSize,
			filter: filters.status && filters.status[0] ? filters.status[0] : "active",
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchItemList();
			}
		);
		scrollTo();
	};
	addRoom() {
		this.props.history.push(`${this.props.match.url}/add-user`);
	}
	delete(id) {
		this.setState({ deleteModal: true, groupUserId: id });
	}


	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	view(id) {
		this.props.history.push(`${this.props.match.url}/view/${id}`);
	}

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
		});
	};

	handleOk = () => {
		const id = this.state.groupUserId;
		this.setState({ confirmLoading: true });
		API.delete(`api/admin/groupUser/${id}`)
			.then((response) => {
				if (response) {
					this.fetchItemList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
						loading: false,
						pageloading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
						loading: false,
						pageloading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				//console.log("error:", error);
				this.setState({
					confirmLoading: false,
					deleteModal: false,
					loading: false,
					pageloading: false,
				});
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchItemList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					page_no: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	handleStatusChange = (id) => {
		const statusData = [];
		statusData["data"] = { item_id: id };
		API.post("api/admin/item_suggestion/change-item-status", statusData).then((response) => {
			if (response) {
				this.fetchItemList({
					page: this.state.pagination.current ? this.state.pagination.current : 1,
				});
			} else {
				message.error(response.message);
			}
		});
	};


	checkBoxHandler = (event, id) => {
		if (event.target.checked) {
			this.setState(prevState => ({
				batchAction: [...prevState.batchAction, id]
			}))
		}
		else {
			this.setState({
				batchAction: this.state.batchAction.filter((data) => {
					return data !== id
				})
			})
		}
	}

	render() {
		const columns = [
			{
				title: "name",
				dataIndex: "company_name",
				key: "company_name",
				width: "40%",
				sorter: true,
				align: "center",
			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				width: "40%",
				align: "center",
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons'>
							{/* <Tooltip title='Edit'>
								<Button
									type='primary'
									className='c-btn c-round c-warning'
									icon='edit'
									onClick={() => this.edit(text.group_id)}></Button>
							</Tooltip> */}
							<Tooltip title='Delete'>
								<Button
									type='primary'
									className='c-btn c-round c-danger'
									icon='delete'
									onClick={() => this.delete(text.company_id)}></Button>
							</Tooltip>

						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Group User Management
							</h2>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}

						</Col>
						<Col sm={10}>
							<Row>
								<Col sm={16} style={{ textAlign: "right" }}>
									<Search
										placeholder='Search user name'
										onChange={(e) => this.handleSearch(e.target.value)}
										value={this.state.search}
										style={{ width: 200 }}
									/>
								</Col>
								{
									<Col sm={8}>
										<Button
											className='addButton'
											style={{ marginTop: "0" }}
											type='primary'
											onClick={() => this.addRoom()}>
											+ Add Group User
										</Button>
										
									</Col>
								}
							
							</Row>
						</Col>


					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem" }}>
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
								}}
								dataSource={this.state.roomList}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete this user?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}

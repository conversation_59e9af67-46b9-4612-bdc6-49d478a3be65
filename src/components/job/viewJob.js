import {
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Popover,
  Row,
  Select,
  Spin,
  Pagination,
  Table,
  Tooltip,
  Menu,
  Dropdown,
  Card,
  Tabs,
  Tag,
} from "antd";
import { saveAs } from "file-saver";
import React from "react";
import DateFns from "date-fns";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import smallImage from "../../image/placeholder_image.png";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import {
  Div,
  ImagesContainer,
  Signature,
  SortContainer,
  SortMenu,
} from "./viewJob.style";
import {
  MOVER_STORAGE_API_URL,
  BASEURL_API,
} from "../../static/data/constants";
import axios from "axios";

const { changeCurrent } = appActions;
const { Meta } = Card;
const { TabPane } = Tabs;
const Search = Input.Search;
const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { Option } = Select;
const pageSize = 30;
const MAX_DESCRIPTION_LENGTH = 40;

class ViewJob extends React.Component {
  state = {
    loading: false,
    contentloader: false,
    contentloaderInventory: false,
    previewVisible: false,
    previewImage: "",
    fileList: [],
    unitList: [],
    inventoryData: {},
    initialInventoryData: {},
    showModal: false,
    shipmentStage: [],
    shipmentStagesTab: [],
    shipmentStage2: [],
    alter_stage_id: null,
    inventoryItemData: {},
    orderingField:
      localStorage.getItem("orderingField") &&
        localStorage.getItem("orderingField") !== "" &&
        localStorage.getItem("ShipmentIdFilter") === this.props.match.params.id
        ? localStorage.getItem("orderingField")
        : "qr_id",
    orderingWay:
      localStorage.getItem("orderingWay") &&
        localStorage.getItem("orderingWay") !== "" &&
        localStorage.getItem("ShipmentIdFilter") === this.props.match.params.id
        ? localStorage.getItem("orderingWay")
        : "ASC",
    sort_field:
      localStorage.getItem("sort_field") &&
        localStorage.getItem("sort_field") !== "" &&
        localStorage.getItem("ShipmentIdFilter") === this.props.match.params.id
        ? localStorage.getItem("sort_field")
        : "Label number",
    visible: false,
    imageItemLoading: false,
    items: [],
    roomList: [],
    active: 1,
    isFirst: 0,
    current_job_stage_check: 0,
    total_stages_check: 0,
    isJobComplete: 0,
    data: [],
    totalCount: 0,
    current: 1,
    pdfTotalItems: 0,
    warehouseId: "",
    integrationKeyStatus: false,
    isPickListCheck: false,
    integrationKey: "",
    consumerLoginAccessToken: "",
    isFirstStage: "",
    scanIntoStorageCheck: "",
    isPickListPdfForStages: false,
    warehousesList: [],
    pdfList: [],
    deleteModalPDF: false,
    deletePdfId: null,
    isHovered: false,
    defaultTabActiveKey: 1,
    jobItemTagList: [],
    jobItemRoomList: [],
    tagsSearchList: [],
    roomsSearchList: [],
    deleteModalShipmentTypeStage: false,
    deleteShipmentStageId: null,
    showModalForAddStage: false,
    isJobCompleteShipmnetTypeInfo: null,
    isJobCompleteInventoryData: null,
    additionalValidationModal: false,
    isMakeAddScanCheckedCurrentStage: false,
    isMainAdmin: false,
    warehouseNameMainAdmin: null,
    search: null,
    currentShipmentStageName: "default_stage",
    currentShipmentStageId: null,
  };
  componentDidMount() {
    if (
      localStorage.getItem("ShipmentIdFilter") !== this.props.match.params.id
    ) {
      localStorage.setItem("orderingField", "qr_id");
      localStorage.setItem("orderingWay", "ASC");
      localStorage.setItem("sort_field", "Label number");
    }

    const userType = localStorage.getItem("userType") === "1" ? true : false;

    this.setState(
      {
        contentloader: true,
        contentloaderInventory: true,
        isMainAdmin: userType,
      },
      () => {
        this.fetchShipmentDetails();
        this.initializeData();
      }
    );
  }

  createConsumerLoginJson = () => {
    return JSON.stringify({
      companyIdTokenMoverInventory: this.state.integrationKey,
      email: "<EMAIL>",
      password: "5PLaRAqq",
      deviceToken: "abcd",
      deviceType: 0,
    });
  };

  setLoadingState = (loading = true) => {
    this.setState({ contentloader: loading, imageItemLoading: loading });
  };

  downloadPdfFile = (response, successMessage = "Pdf Downloaded!") => {
    const { job_number, shipment_name } = this.state.inventoryData;
    if (response) {
      const data = Uint8Array.from(response.data.data);
      const content = new Blob([data.buffer]);
      saveAs(content, `${job_number}-${shipment_name}.pdf`);
      this.setLoadingState(false);
      message.success(successMessage);
    } else {
      message.error(response.message);
      this.setLoadingState(false);
    }
  };

  createApiData = (params) => {
    const data = [];
    data["data"] = params;
    return data;
  };

  initializeData = async () => {
    try {
      // Run multiple API calls concurrently for better performance
      const [integrationResponse] = await Promise.allSettled([
        API.get(`api/admin/integrationKey/fetch`),
        this.fetchJobItemTagList(),
        this.fetchJobItemRoomList()
      ]);

      if (integrationResponse.status === 'fulfilled' && integrationResponse.value) {
        const response = integrationResponse.value;
        if (response.data.status === "active") {
          this.setState({
            integrationKeyStatus: true,
            integrationKey: response.data.integration_key,
          });
          this.consumerLoginJson();
        } else {
          this.setState({
            contentloader: false,
            integrationKeyStatus: false,
          });
        }
      } else {
        this.setState({ contentloader: false });
      }
    } catch (error) {
      this.setState({ contentloader: false });
    }
  };

  consumerLoginJson = () => {
    const consumerLoginJson = this.createConsumerLoginJson();

    axios
      .post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
        headers: {
          "Content-Type": "application/json",
        },
      })
      .then((consumerLoginResponse) => {
        this.setState({
          contentloader: false,
          loading: false,
          consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken,
        });
        this.fetchCompanyResponse();
      })
      .catch((error) => {
        this.setState({ contentloader: false, loading: false });
      });
  };

  fetchCompanyResponse = () => {
    const fetchCompanyJson = JSON.stringify({
      companyIdTokenMoverInventory: this.state.integrationKey,
    });

    axios
      .post(
        `${MOVER_STORAGE_API_URL}import/mover-inventory/companies`,
        fetchCompanyJson,
        {
          headers: {
            "Content-Type": "application/json",
            accessToken: this.state.consumerLoginAccessToken,
          },
        }
      )
      .then((fetchCompanyResponse) => {
        this.setState({
          contentloader: false,
          loading: false,
          warehousesList: fetchCompanyResponse.data.data.warehouses,
        });
      })
      .catch((error) => {
        this.setState({ contentloader: false, loading: false });
      });
  };

  fetchJobItemTagList = () => {
    let params = {
      shipmentId: this.props.match.params.id,
    };
    const data = this.createApiData(params);
    return API.post(`api/home/<USER>
      .then((response) => {
        if (response) {
          this.setState({
            jobItemTagList: response.data,
          });
        } else {
          message.error(response.message);
        }
        return response;
      })
      .catch((error) => {
        throw error;
      });
  };
  fetchJobItemRoomList = () => {
    let params = {
      shipmentId: this.props.match.params.id,
    };
    const data = this.createApiData(params);
    return API.post(`api/home/<USER>
      .then((response) => {
        if (response) {
          this.setState({
            jobItemRoomList: response.data,
          });
        } else {
          message.error(response.message);
        }
        return response;
      })
      .catch((error) => {
        throw error;
      });
  };

  fetchShipmentDetails = async () => {
    this.setState({ isFirst: this.state.isFirst + 1 });
    const id = this.props.match.params.id;
    this.props.changeCurrent("job");

    try {
      const response = await API.get(
        `api/admin/shipment/${id}?orderingField=${this.state.orderingField}&orderingWay=${this.state.orderingWay}`
      );

      if (!response || !response.data) {
        message.error((response && response.message) || "Failed to fetch shipment details");
        this.setLoadingState(false);
        return;
      }

      const data = response.data;
      localStorage.setItem("ShipmentIdFilter", id);

      // Cache frequently accessed data
      const shipmentTypeData = data.shipment_type_for_shipment;
      const localShipmentStage = (shipmentTypeData && shipmentTypeData.local_shipment_stage) || [];

      const defaultStage = {
        local_shipment_stage_id: -1,
        name: "Show All Items",
        status: "active",
        order_of_stages: 0,
        local_shipment_type_id: 0,
        ref_shipment_stage_id: 0,
        shipment_job_id: 0,
        is_default: true
      };

      const shipmentStagesTab = localShipmentStage.length > 0
        ? [defaultStage, ...localShipmentStage]
        : [defaultStage];

      // Optimize state updates - batch them into a single setState call
      const newState = {
        shipmentStagesTab,
        initialInventoryData: this.state.isFirst === 1 ? data : this.state.initialInventoryData,
        inventoryData: data,
        pdfTotalItems: data.total_items,
        shipmentStage: localShipmentStage,
        inventoryItemData: data.job_items || [],
        items: data.job_items || [],
        warehouseId: data.warehouseId,
        unitList: data.job_warehouses || [],
        imageItemLoading: false,
        current_job_stage_check: (shipmentTypeData && shipmentTypeData.current_job_stage) || 0,
        total_stages_check: (data.shipment_type && data.shipment_type.total_stages) || 0,
        isJobComplete: data.is_job_complete_flag || 0,
        contentloader: false,
        isFirstStage: data.is_first_stage,
        scanIntoStorageCheck: data.assign_storage_units_to_items,
        pdfList: data.job_documents || [],
      };

      // Check for pick list requirements efficiently
      const hasPickListItems = localShipmentStage.some(stage => stage.assign_storage_units_to_items === 1);
      if (hasPickListItems) {
        newState.isPickListCheck = true;
      }

      // Filter stages efficiently
      const currentJobStage = (shipmentTypeData && shipmentTypeData.current_job_stage) || 0;
      const shipmentStage2 = localShipmentStage.filter(stage => stage.order_of_stages < currentJobStage);

      const hasPickListPdfStages = shipmentStage2.some(stage => stage.assign_storage_units_to_items === 1);
      if (hasPickListPdfStages) {
        newState.isPickListPdfForStages = true;
      }

      newState.shipmentStage2 = shipmentStage2;

      this.setState(newState);
      // Handle warehouse data asynchronously without blocking
      if (data.storage_shipment_job_id) {
        this.fetchWarehouseData(data.storage_shipment_job_id);
      }

      // Start parallel operations for better performance
      const promises = [
        this.fetchItemListDetails(),
        this.fetchRoomList(data.job_items || [])
      ];

      await Promise.allSettled(promises);

    } catch (error) {
      console.error("Error fetching shipment details:", error);
      this.setState({
        contentloader: false,
        imageItemLoading: false,
        confirmLoading: false,
      });
    }
  };

  // Optimized warehouse data fetching - non-blocking
  fetchWarehouseData = async (storageShipmentJobId) => {
    try {
      const consumerLoginJson = this.createConsumerLoginJson();

      const loginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
        headers: { "Content-Type": "application/json" },
      });

      const warehouseResponse = await axios.get(
        `${MOVER_STORAGE_API_URL}import/mover-inventory/get-warehouse-from-shipment/${storageShipmentJobId}`,
        {
          headers: {
            "Content-Type": "application/json",
            accessToken: loginResponse.data.data.accessToken,
          },
        }
      );

      this.setState({
        warehouseNameMainAdmin: warehouseResponse.data.data.name,
      });
    } catch (error) {
      console.error("Error fetching warehouse data:", error);
    }
  };

  // Optimized room list processing with better algorithm
  fetchRoomList = async (jobItems) => {
    try {
      const response = await API.get("api/admin/customer_room/list?page_size=1000000");

      if (response && response.data && response.data.rows && jobItems.length > 0) {
        // Create a Set for O(1) lookup instead of nested loops
        const jobItemRoomIds = new Set(jobItems.map(item => item.room_id));

        // Use Map to avoid duplicate rooms efficiently
        const roomsMap = new Map();

        response.data.rows.forEach(room => {
          if (jobItemRoomIds.has(room.shipment_room_id)) {
            roomsMap.set(room.shipment_room_id, room);
          }
        });

        this.setState({
          roomList: Array.from(roomsMap.values()),
          confirmLoading: false,
        });
      }
    } catch (error) {
      console.error("Error fetching room list:", error);
      this.setState({ confirmLoading: false });
    }
  };

  fetchItemListDetails = () => {
    const id = this.props.match.params.id;
    this.setState({
      contentloader: true,
      contentloaderInventory: true,
      imageItemLoading: true,
    });

    const params = {
      tagIds: this.state.tagsSearchList,
      roomIds: this.state.roomsSearchList,
      orderingField: this.state.orderingField,
      orderingWay: this.state.orderingWay,
      page_size: pageSize,
      page_no: this.state.current,
      shipmentId: id,
      search: this.state.search,
      currentShipmentStageId: this.state.currentShipmentStageName === "default_stage" ? null : this.state.currentShipmentStageId,
      currentShipmentStageName: this.state.currentShipmentStageName === "default_stage" ? null : this.state.currentShipmentStageName,
    };
    
    console.log("🚀 ~ ViewJob ~ params:", params)

    const data = this.createApiData(params);
    return API.post(`api/home/<USER>/cms-web`, data)
      .then((response) => {
        if (response) {
          this.setState({
            contentloader: false,
            contentloaderInventory: false,
            imageItemLoading: false,
            data: response.data.rows,
            totalCount: response.data.count,
            initialInventoryData: {
              ...this.state.initialInventoryData,
              itemData: response.data.rows,
            },
          });
        } else {
          message.error(response.message);
          this.setState({
            contentloader: false,
            contentloaderInventory: false,
            imageItemLoading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({
          contentloader: false,
          contentloaderInventory: false,
          imageItemLoading: false,
        });
      });
  };

  handleChangePagination = (page) => {
    this.setState(
      {
        current: page,
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  viewInventory(id) {
    this.props.history.push({
      pathname: `/job/view-inventory/${id}`,
      state: { initialStage: this.state.shipmentStage[0].name, shipmentStage: this.state.shipmentStage },
    });
  }
  handlePreview = (file) => {
    this.setState({
      previewImage: file.thumbUrl,
      previewVisible: true,
    });
  };
  handleUpload = ({ fileList }) => {
    this.setState({ fileList });
  };

  onNewPdf = async () => {
    this.setLoadingState(true);
    let params = {
      shipmentId: this.props.match.params.id,
      isPrintPhotos: 0,
      isDownloadByCustomer: 0,
    };
    const data = this.createApiData(params);
    API.post(`api/admin/shipment/pdf`, data)
      .then((response) => {
        this.downloadPdfFile(response);
      })
      .catch((error) => {
        this.setLoadingState(false);
      });
  };

  onNewPdfWithPhotos = async () => {
    this.setLoadingState(true);
    let params = {
      shipmentId: this.props.match.params.id,
      isPrintPhotos: 1,
      isDownloadByCustomer: 0,
    };
    const data = this.createApiData(params);
    API.post(`api/admin/shipment/pdf`, data)
      .then((response) => {
        this.downloadPdfFile(response);
      })
      .catch((error) => {
        this.setLoadingState(false);
      });
  };

  csvExportShipment = async () => {
    try {
      this.setLoadingState(true);
      const BASEURL = BASEURL_API;

      let params = {
        shipmentId: this.props.match.params.id,
      };

      const data = this.createApiData(params);

      API.post("api/admin/shipment/csv/export", data)
        .then((response) => {
          window.open(`${BASEURL}${response.data}`, "blank");

          this.setLoadingState(false);
          message.success("CSV File Downloaded!");
        })
        .catch((error) => {
          this.setLoadingState(false);
        });
    } catch (error) {
      this.setLoadingState(false);
    }
  };

  excelExportShipment = async () => {
    try {
      this.setLoadingState(true);
      const BASEURL = BASEURL_API;

      let params = {
        shipmentId: this.props.match.params.id,
      };

      const data = this.createApiData(params);

      API.post("api/admin/shipment/xlsx/export", data)
        .then((response) => {
          window.open(`${BASEURL}${response.data}`, "blank");
          this.setLoadingState(false);
          message.success("Excel File Downloaded!");
        })
        .catch((error) => {
          this.setLoadingState(false);
        });
    } catch (error) {
      this.setLoadingState(false);
    }
  };

  onNewPicklistPdf = async () => {
    this.setLoadingState(true);
    let params = {
      shipmentId: this.props.match.params.id,
    };
    const data = this.createApiData(params);
    API.post(`api/admin/shipment/picklistPdf`, data)
      .then((response) => {
        this.downloadPdfFile(response);
      })
      .catch((error) => {
        this.setLoadingState(false);
      });
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFieldsAndScroll((err, values) => {
      let params = {
        reason: values.reason,
        altered_stage_id: this.state.alter_stage_id,
      };
      if (!err) {
        const data = this.createApiData(params);
        this.setState({ loading: true });

        API.put(
          "api/admin/shipment/force_stage_change/" + this.props.match.params.id,
          data
        )
          .then((response) => {
            if (response) {
              this.setState({ loading: false });
              message.success(response.message);
              this.props.history.goBack();
            } else {
              this.setState({ loading: false });
              message.error(response.message);
            }
          })
          .catch((error) => {
            this.setState({ loading: false });
          });
      }
    });
  };
  roomClikAPI = (active, e) => {
    this.setState({
      items: this.state.inventoryItemData.filter(
        (data) => data.room_id === parseInt(e.target.value)
      ),
      active: active,
    });
  };
  allClickAPI = () => {
    this.setState({
      items: this.state.inventoryItemData,
      active: 1,
    });
  };
  sortClickAPI = (title, field, direction) => {
    localStorage.setItem("orderingField", field);
    localStorage.setItem("orderingWay", direction);
    localStorage.setItem("sort_field", title);

    this.setState(
      {
        orderingField: field,
        orderingWay: direction,
        sort_field: title,
        imageItemLoading: true,
      },
      () => {
        this.fetchShipmentDetails();
        this.hide();
      }
    );
  };
  handleVisibleChange = (visible) => {
    this.setState({ visible });
  };
  hide = () => {
    this.setState({
      visible: false,
    });
  };
  onShowModal = () => {
    this.setState({ showModal: true });
  };
  onCancelModal = () => {
    this.setState({ showModal: false });
  };
  changeValue = (value) => {
    this.setState({ alter_stage_id: value });
  };

  allowCancelToAddNewStage = () => {
    this.setState({ showModalForAddStage: false });
  };

  allowToAddNewStage = () => {
    const {
      isJobCompleteShipmnetTypeInfo,
      isJobCompleteInventoryData,
    } = this.state;
    const pathname = "/job/add/shipment-type-stage";
    const state = {
      shipmnetTypeInfo: isJobCompleteShipmnetTypeInfo,
      inventoryData: isJobCompleteInventoryData,
    };

    this.props.history.push({
      pathname,
      state,
    });
  };

  addShipmentTypeStage(data, inventoryData) {
    if (this.state && this.state.isJobComplete == 1) {
      this.setState({
        showModalForAddStage: true,
        isJobCompleteShipmnetTypeInfo: data,
        isJobCompleteInventoryData: inventoryData,
      });
    } else {
      this.props.history.push({
        pathname: `/job/add/shipment-type-stage`,
        state: { shipmnetTypeInfo: data, inventoryData },
      });
    }
  }

  viewShipmentStage(id, inventoryData) {
    let isCurrentStage = id === inventoryData.local_job_status ? true : false;
    this.props.history.push({
      pathname: `/job/view/shipment-type-stage/${id}`,
      state: { inventoryData, isCurrentStage },
    });
  }

  editShipmentStage(id, inventoryData) {
    this.props.history.push({
      pathname: `/job/edit/shipment-type-stage/${id}`,
      state: { inventoryData },
    });
  }

  deleteShipmentStage = (data) => {
    const stageId = data.local_shipment_stage_id;
    const currentStageData = data;
    const checks = this.state.shipmentStage;
    const currentStageNumber = data.order_of_stages;
    function findOrder(checks, find) {
      for (let i = 0; i < checks.length; i++) {
        if (checks[i].order_of_stages === find) {
          return checks[i];
        }
      }
      return null;
    }
    let nextStage = findOrder(checks, currentStageNumber + 1);

    if (
      currentStageData.add_items_to_inventory &&
      nextStage !== null &&
      nextStage.scan_require
    ) {
      this.setState({
        additionalValidationModal: true,
        isMakeAddScanCheckedCurrentStage: true,
      });
    } else if (
      currentStageData.remove_items_to_inventory &&
      nextStage !== null &&
      nextStage.remove_scan_require
    ) {
      this.setState({
        additionalValidationModal: true,
        isMakeAddScanCheckedCurrentStage: false,
      });
    } else {
      this.setState({
        deleteModalShipmentTypeStage: true,
        deleteShipmentStageId: stageId,
      });
    }
  };

  handleOkShipmentTypeStage = () => {
    const stageId = this.state.deleteShipmentStageId;
    this.setState({ confirmLoading: true });
    API.post(`api/admin/shipmentTypeStage/${stageId}`)
      .then((response) => {
        if (response) {
          this.setState({
            deleteModalShipmentTypeStage: false,
            confirmLoading: false,
          });
          this.fetchShipmentDetails();
          message.success(response.message);
        } else {
          this.setState({
            deleteModalShipmentTypeStage: false,
            confirmLoading: false,
          });
          this.fetchShipmentDetails();
          message.error(response.message);
        }
      })
      .catch((error) => {
        this.setState({
          confirmLoading: false,
          deleteModalShipmentTypeStage: false,
        });
      });
  };

  handleCancelShipmentTypeStage = () => {
    this.setState({
      deleteModalShipmentTypeStage: false,
    });
  };

  jobPrint = () => {
    if (this.props.match.params.id) {
      let params = {
        responseType: "blob",
      };
      const data = this.createApiData(params);
      this.setState({ loading: true });
      API.post(
        "api/admin/shipment/" + this.props.match.params.id + "/print_summary/",
        data
      )
        .then((response) => {
          const pdfBlob = new Blob([response], {
            type: "application/pdf",
          });
          saveAs(pdfBlob, "jobdetails.pdf");
          if (response) {
            this.setState({ loading: false });
            message.success("Pdf generated successfully.");
          } else {
            this.setState({ loading: false });
            message.error("Error while creating pdf.");
          }
        })
        .catch((error) => {
          this.setState({ loading: false });
        });
    } else {
      this.setState({ loading: false });
      message.error("Error while creating pdf.");
    }
  };

  countException = (data) => {
    let count = 0;
    data.forEach((data) => {
      if (data.exceptions.length > 0) {
        count = count + 1;
      } else {
        //console.log(data.shipment_inventory_exceptions);
        //console.log("Without exception");
      }
    });
    return count;
  };

  countDisassembled = (data) => {
    let count = 0;
    data.forEach((data) => {
      if (data.is_disassembled === "YES") {
        count = count + 1;
      } else {
        //console.log(data.shipment_inventory_exceptions);
        //console.log("Without exception");
      }
    });
    return count;
  };

  countElectronics = (data) => {
    let count = 0;
    data.forEach((data) => {
      if (data.is_electronics === "YES") {
        count = count + 1;
      }
    });
    return count;
  };

  countHighValueItem = (data) => {
    let count = 0;
    data.forEach((data) => {
      if (data.is_high_value === "true") {
        count = count + 1;
      }
    });
    return count;
  };

  toDate = (date) => {
    let s = new Date(date).toLocaleTimeString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
    return s;
  };

  downloadPdf = (fileUrl, fileName) => {
    this.setState({ loading: true, contentloader: true });
    fetch(fileUrl)
      .then((response) => response.blob())
      .then((blob) => {
        this.setState({ loading: false, contentloader: false });
        saveAs(blob, fileName);
      })
      .catch((error) => {
        this.setState({ loading: false, contentloader: false });
        console.error("Error downloading the PDF:", error);
      });
  };

  handleOkPDF = () => {
    let shipmentId = this.props.match.params.id;
    const deleteData = this.createApiData({ pdfId: this.state.deletePdfId });
    this.setState({ loading: true, contentloader: true });
    API.delete(`api/admin/shipment/delete/documents/${shipmentId}`, deleteData)
      .then((response) => {
        if (response) {
          this.fetchShipmentDetails();
          this.setState({
            deleteModalPDF: false,
            loading: false,
            contentloader: false,
          });
          message.success(response.message);
        } else {
          this.fetchShipmentDetails();
          this.setState({
            deleteModalPDF: false,
            loading: false,
            contentloader: false,
          });
          message.error(response.message);
        }
      })
      .catch((error) => {
        message.error(error.message);
        this.setState({
          loading: false,
          contentloader: false,
          imageItemLoading: false,
          deleteModalPDF: false,
        });
        this.fetchShipmentDetails();
      });
  };

  deletePDF(id) {
    this.setState({
      deleteModalPDF: true,
      deletePdfId: id,
    });
  }

  handleCancelPDF = () => {
    this.setState({ deleteModalPDF: false, confirmLoading: false });
  };

  handleFileChange = (event) => {
    const files = Array.from(event.target.files);

    const pdfFiles = files.filter((file) => file.type === "application/pdf");
    const nonPdfFiles = files.filter((file) => file.type !== "application/pdf");

    if (nonPdfFiles.length > 0) {
      message.error("Please upload only PDF files.");
      return;
    } else {
      this.setState({
        loading: true,
        contentloader: true,
        imageItemLoading: true,
      });
      let shipmentId = this.props.match.params.id;
      let formData = new FormData();

      pdfFiles.forEach((uploadFile, index) => {
        formData.append(`file`, uploadFile);
      });

      return API.post(`api/admin/shipment/add/documents/${shipmentId}`, {
        data: formData,
      })
        .then((response) => {
          if (response.status === 1) {
            message.success(response.message);
            this.fetchShipmentDetails();
          } else {
            message.error(response.message);
            this.setState({
              loading: false,
              contentloader: false,
              imageItemLoading: false,
            });
          }
        })
        .catch((error) => {
          message.error("An error occurred while uploading files.");
        });
    }
  };

  customerPdfSend = (customerJob, isPrintPhotos) => {
    let params = {
      shipmentId: this.props.match.params.id,
      customer_id: customerJob.customer_id,
      isPrintPhotos: isPrintPhotos,
      isDownloadByCustomer: 0,
    };
    const data = this.createApiData(params);
    this.setState({ contentloader: true });

    API.post("api/admin/customer/pdf", data)
      .then((response) => {
        if (response) {
          this.setState({ contentloader: false });
          message.success(response.message);
        } else {
          this.setState({ contentloader: false });
          message.error(response.message);
        }
      })
      .catch((error) => {
        this.setState({ contentloader: false });
      });
  };

  handleHover = (isHovered) => {
    this.setState({ isHovered });
  };

  tabChangeHandler = (activeKey) => {
    const tabIndex = parseInt(activeKey);
    const selectedStage = this.state.shipmentStagesTab[tabIndex];
    if (selectedStage.is_default == true) {
      this.setState({
        currentShipmentStageName: "default_stage",
        currentShipmentStageId: null,
      });
    }
    else if (selectedStage.add_items_to_inventory) {
      this.setState({
        currentShipmentStageName: "add_items_to_inventory",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.assign_storage_units_to_items) {
      this.setState({
        currentShipmentStageName: "add_items_to_storage",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.unassign_storage_units_from_items) {
      this.setState({
        currentShipmentStageName: "remove_items_from_storage",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.remove_items_to_inventory) {
      this.setState({
        currentShipmentStageName: "remove_items_from_inventory",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.remove_scan_require) {
      this.setState({
        currentShipmentStageName: "remove_scan_require",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.scan_require) {
      this.setState({
        currentShipmentStageName: "additional_scan_require",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else {
      this.setState({
        currentShipmentStageName: "default_stage",
        currentShipmentStageId: null,
      });
    }
    this.setState(
      {
        activeTabKey: activeKey,
        current: 1,
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  handleChangeJobItemTagList = (item) => {
    this.setState(
      {
        tagsSearchList: item.map((item) => item.key),
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  handleChangeJobItemRoomList = (item) => {
    this.setState(
      {
        roomsSearchList: item.map((item) => item.key),
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  generateDataSource = () => {
    const { shipmentStage, current_job_stage_check } = this.state;

    const filteredStages = shipmentStage.filter(
      (stage) => stage.order_of_stages <= current_job_stage_check
    );

    let total_inventory_items_previous_stage = 0;
    let total_storage_items_previous_stage = 0;
    let total_remove_items_storage_previous_stage = 0;
    let total_remove_items_inventory_previous_stage = 0;
    let total_inventory_items_previous_stage_weight = 0;
    let total_remove_inventory_items_previous_stage_weight = 0;
    let total_storage_items_previous_stage_weight = 0;
    let total_remove_storage_items_previous_stage_weight = 0;
    let total_add_scan_items_previous_stage = 0;
    let total_remove_scan_item_previous_stage = 0;

    filteredStages.forEach((stage, index) => {
      total_inventory_items_previous_stage +=
        stage.total_add_items_inventory_stage;
      total_storage_items_previous_stage += stage.total_add_items_storage_stage;
      total_remove_items_storage_previous_stage +=
        stage.total_remove_items_storage;
      total_remove_items_inventory_previous_stage +=
        stage.total_remove_items_inventory_stage;
      total_inventory_items_previous_stage_weight += Math.round(
        stage.total_items_weight_in_shipment
      );
      total_remove_inventory_items_previous_stage_weight += Math.round(
        stage.total_remove_item_weight_in_shipment
      );
      total_storage_items_previous_stage_weight += Math.round(
        stage.total_items_weight_in_storage
      );
      total_remove_storage_items_previous_stage_weight += Math.round(
        stage.total_remove_items_weight_from_storage
      );
      total_add_scan_items_previous_stage +=
        stage.total_add_items_to_inventory_scan;
      total_remove_scan_item_previous_stage +=
        stage.total_remove_scan_item_previous_stage;

      stage.total_inventory_items_previous_stage = total_inventory_items_previous_stage;
      stage.total_storage_items_previous_stage = total_storage_items_previous_stage;
      stage.total_remove_items_storage_previous_stage = total_remove_items_storage_previous_stage;
      stage.total_remove_items_inventory_previous_stage = total_remove_items_inventory_previous_stage;
      stage.total_inventory_items_previous_stage_weight = Math.round(
        total_inventory_items_previous_stage_weight
      );
      stage.total_remove_inventory_items_previous_stage_weight = Math.round(
        total_remove_inventory_items_previous_stage_weight
      );
      stage.total_storage_items_previous_stage_weight = Math.round(
        total_storage_items_previous_stage_weight
      );
      stage.total_remove_storage_items_previous_stage_weight = Math.round(
        total_remove_storage_items_previous_stage_weight
      );

      stage.total_add_scan_items_previous_stage = total_add_scan_items_previous_stage;
      stage.total_remove_scan_item_previous_stage = total_remove_scan_item_previous_stage;
    });

    const grandTotals = this.calculateGrandTotals(filteredStages);

    return [...filteredStages, grandTotals];
  };

  calculateGrandTotals = (stageData) => {
    let grandTotalItemsInShipment = 0;
    let grandTotalItemsInStorage = 0;
    let grandTotalItemsRemoveStorage = 0;
    let grandTotalItemsRemoveInventory = 0;
    let grandTotalWeightInShipment = 0;
    let grandTotalWeightInStorage = 0;
    let grandTotalItemsOfInventory = 0;
    let grandTotalItemsOfStorage = 0;
    let grandTotalAddScanItemsPreviousStage = 0;
    let grandTotalRemoveScanItemsPreviousStage = 0;

    let grandTotal = 0;

    stageData &&
      stageData.length > 0 &&
      stageData.forEach((item) => {
        grandTotalItemsInShipment += Math.round(
          item.total_add_items_inventory_stage
        );
        grandTotalItemsInStorage += Math.round(
          item.total_add_items_storage_stage
        );
        grandTotalItemsRemoveStorage += Math.round(
          item.total_remove_items_storage_stage
        );
        grandTotalItemsRemoveInventory += Math.round(
          item.total_remove_items_inventory_stage
        );
        grandTotalItemsOfInventory += Math.round(item.total_items_inventory);
        grandTotalItemsOfStorage += Math.round(item.total_items_storage);
        grandTotalWeightInShipment += Math.round(
          item.total_items_weight_in_shipment
        );
        grandTotalWeightInStorage += Math.round(
          item.total_items_weight_in_storage
        );
        grandTotalAddScanItemsPreviousStage += Math.round(
          item.total_add_items_to_inventory_scan
        );
        grandTotalRemoveScanItemsPreviousStage += Math.round(
          item.total_remove_items_to_inventory_scan
        );
      });

    const extraRow = {
      key: "grandTotal",
      name: "Cumulative Total",
      total_add_items_inventory_stage: grandTotalItemsInShipment,
      total_add_items_storage_stage: grandTotalItemsInStorage,
      total_remove_items_storage_stage: grandTotalItemsRemoveStorage,
      total_remove_items_inventory_stage: grandTotalItemsRemoveInventory,
      total_items_inventory: 0,
      total_items_storage: 0,
      total_items_weight_in_shipment: 0,
      total_items_weight_in_storage: 0,
      total_inventory_items_previous_stage: 0,
      total_storage_items_previous_stage: 0,
      total_inventory_items_previous_stage_weight: 0,
      total_remove_items_storage_previous_stage: 0,
      total_remove_items_inventory_previous_stage: 0,
      total_storage_items_previous_stage_weight: 0,
      total_remove_inventory_items_previous_stage_weight: 0,
      total_remove_storage_items_previous_stage_weight: 0,
      total_add_items_to_inventory_scan: grandTotalAddScanItemsPreviousStage,
      total_remove_items_to_inventory_scan: grandTotalRemoveScanItemsPreviousStage,
      grand_totel: grandTotal,
    };
    return extraRow;
  };

  handleCancel = () => {
    this.setState({ additionalValidationModal: false, loading: false });
  };

  handleSearch = (e) => {
    const searchValue = e;
    this.setState({ search: searchValue, current: 1 }, () => {
      this.fetchItemListDetails();
      this.hide();
    });
  };

  render() {
    const dataSource = this.generateDataSource().map((item, index, array) => ({
      ...item,
      isLast: index === array.length - 1,
    }));

    const sortArray = this.state.data;
    const newArray = sortArray.sort(function (a, b) {
      const nameA = a.type.toUpperCase(); // ignore upper and lowercase
      const nameB = b.type.toUpperCase(); // ignore upper and lowercase
      if (nameA > nameB) {
        return -1;
      }
      if (nameA < nameB) {
        return 1;
      }

      // names must be equal
      return 0;
    });

    const { getFieldDecorator } = this.props.form;

    const getOverlayOpacity = (data) => {
      if (this.state.currentShipmentStageName === "default_stage") {
        if (data.is_item_scanned_remove_from_storage) {
          return 1;
        }
        return 0;
      }
      if (this.state.currentShipmentStageName === "add_items_to_inventory") {
        if (data.isManualLabel === true || data.isManualLabel === "true") {
          return 1;
        }
        return 0;
      }
      if (this.state.currentShipmentStageName === "additional_scan_require") {
        return 0;
      }
      if (this.state.currentShipmentStageName === "add_items_to_storage") {
        return 0;
      }
      if (this.state.currentShipmentStageName === "remove_items_from_storage") {
        return 0;
      }
      if (this.state.currentShipmentStageName === "remove_scan_require") {
        return 0;
      }
      else if (data.is_item_scanned_remove_from_storage) {
        return 0;
      }
      return 0;
    };

    const menu = (
      <Menu>
        <Menu.Item key="1">
          <Button type="primary" icon="printer" onClick={() => this.onNewPdf()}>
            Print PDF
          </Button>
        </Menu.Item>
        <Menu.Item key="1">
          <Button
            type="primary"
            icon="printer"
            onClick={() => this.onNewPdfWithPhotos()}
          >
            Print PDF With Photos
          </Button>
        </Menu.Item>
        <Menu.Item key="2">
          <Button
            type="primary"
            icon="printer"
            onClick={() => this.excelExportShipment()}
          >
            Excel File Export
          </Button>
        </Menu.Item>
        <Menu.Item key="3">
          <Button
            type="primary"
            icon="printer"
            onClick={() => this.csvExportShipment()}
          >
            CSV File Export
          </Button>
        </Menu.Item>
        <Menu.Item key="4">
          {this.state.scanIntoStorageCheck === "yes" ||
            this.state.isPickListPdfForStages === true ? (
            <Button
              type="primary"
              icon="printer"
              onClick={() => this.onNewPicklistPdf()}
            >
              Print Picklist PDF
            </Button>
          ) : (
            ""
          )}
        </Menu.Item>
      </Menu>
    );

    const customerManu = (
      <Menu>
        <Menu.Item key="1">
          <Button
            type="primary"
            icon="printer"
            onClick={() =>
              this.customerPdfSend(this.state.inventoryData.customer_job, 0)
            }
          >
            Send PDF
          </Button>
        </Menu.Item>
        <Menu.Item key="1">
          <Button
            type="primary"
            icon="printer"
            onClick={() =>
              this.customerPdfSend(this.state.inventoryData.customer_job, 1)
            }
          >
            Send PDF With Photos
          </Button>
        </Menu.Item>
      </Menu>
    );

    const formItemLayout = {
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: {
          span: 12,
          offset: 1,
        },
        md: {
          span: 12,
          offset: 1,
        },
        lg: {
          span: 12,
          offset: 1,
        },
        xl: {
          span: 10,
          offset: 1,
        },
      },
    };

    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 6,
        },
        sm: {
          span: 16,
          offset: 6,
        },
        xl: {
          offset: 6,
        },
      },
    };

    const columns = [
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        sorter: true,
      },
      {
        title: "Order of stage",
        dataIndex: "order_of_stages",
        key: "order_of_stages",
        sorter: true,
      },
      {
        title: "Add Items To Inventory",
        dataIndex: "add_items_to_inventory",
        key: "add_items_to_inventory",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.add_items_to_inventory === "1" ||
                record.add_items_to_inventory === 1 ||
                record.add_items_to_inventory === true ||
                record.add_items_to_inventory === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Additional Scan",
        dataIndex: "scan_require",
        key: "scan_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.scan_require === "1" ||
                record.scan_require === 1 ||
                record.scan_require === true ||
                record.scan_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Add Items To Storage",
        dataIndex: "assign_storage_units_to_items",
        key: "assign_storage_units_to_items",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.assign_storage_units_to_items === "1" ||
                record.assign_storage_units_to_items === 1 ||
                record.assign_storage_units_to_items === true ||
                record.assign_storage_units_to_items === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Remove Items From Storage",
        dataIndex: "unassign_storage_units_from_items",
        key: "unassign_storage_units_from_items",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.unassign_storage_units_from_items === "1" ||
                record.unassign_storage_units_from_items === 1 ||
                record.unassign_storage_units_from_items === true ||
                record.unassign_storage_units_from_items === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Remove Items From Inventory",
        dataIndex: "remove_items_to_inventory",
        key: "remove_items_to_inventory",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.remove_items_to_inventory === "1" ||
                record.remove_items_to_inventory === 1 ||
                record.remove_items_to_inventory === true ||
                record.remove_items_to_inventory === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Additional Scan",
        dataIndex: "remove_scan_require",
        key: "remove_scan_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.remove_scan_require === "1" ||
                record.remove_scan_require === 1 ||
                record.remove_scan_require === true ||
                record.remove_scan_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },

      {
        title: "Enable Partial Complete Stage",
        dataIndex: "enable_partial_complete_stage",
        key: "enable_partial_complete_stage",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.enable_partial_complete_stage === "1" ||
                record.enable_partial_complete_stage === 1 ||
                record.enable_partial_complete_stage === true ||
                record.enable_partial_complete_stage === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Carrier Signature Required",
        dataIndex: "supervisor_signature_require",
        key: "supervisor_signature_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.supervisor_signature_require === "1" ||
                record.supervisor_signature_require === 1 ||
                record.supervisor_signature_require === true ||
                record.supervisor_signature_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Customer Signature Required",
        dataIndex: "customer_signature_require",
        key: "customer_signature_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.customer_signature_require === "1" ||
                record.customer_signature_require === 1 ||
                record.customer_signature_require === true ||
                record.customer_signature_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Created Date",
        dataIndex: "created_at",
        key: "created_at",
        sorter: true,
        align: "center",
        render: (text, record) => {
          return (
            <div>
              {" "}
              {record.date ? DateFns.format(record.date, "MM/DD/YYYY") : ""}
            </div>
          );
        },
      },
      {
        title: "Action",
        key: "action",
        align: "center",
        minWidth: "20%",
        render: (record, text) => {
          return (
            <div className="icons">
              {text.order_of_stages > this.state.current_job_stage_check ? (
                <>
                  <Tooltip title="View">
                    <Button
                      type="primary"
                      className="c-btn c-round c-success"
                      icon="eye"
                      onClick={() =>
                        this.viewShipmentStage(
                          text.local_shipment_stage_id,
                          this.state.inventoryData
                        )
                      }
                    ></Button>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <Button
                      type="primary"
                      className="c-btn c-round c-warning"
                      icon="edit"
                      onClick={() =>
                        this.editShipmentStage(
                          text.local_shipment_stage_id,
                          this.state.inventoryData
                        )
                      }
                    ></Button>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <Button
                      type="primary"
                      className="c-btn c-round c-danger"
                      icon="delete"
                      onClick={() => this.deleteShipmentStage(text)}
                      disabled={
                        text.local_shipment_stage_id <
                        (this.state.current_job_stage_check &&
                          this.state.current_job_stage_check)
                      }
                    ></Button>
                  </Tooltip>
                </>
              ) : (
                <Tooltip title="View">
                  <Button
                    type="primary"
                    className="c-btn c-round c-success"
                    icon="eye"
                    onClick={() =>
                      this.viewShipmentStage(
                        text.local_shipment_stage_id,
                        this.state.inventoryData
                      )
                    }
                  ></Button>
                </Tooltip>
              )}
            </div>
          );
        },
      },
    ];

    const storageColumn = [
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        width: "10%",
        align: "center",
        render: (text, record) => {
          return (
            <div className="table-cell-font" style={{ textAlign: "left" }}>
              {record.order_of_stages
                ? `${record.order_of_stages} - ${record.name}`
                : record.name}
            </div>
          );
        },
      },
      {
        title: "Date Completed",
        dataIndex: "created_at",
        key: "created_at",
        width: "5%",
        align: "center",
        render: (text, record) => {
          return (
            <div className="table-cell-font">
              {" "}
              {record.created_at ? this.toDate(record.created_at) : "-"}{" "}
            </div>
          );
        },
      },
      {
        title: "# Of Items Added To Inventory",
        dataIndex: "total_add_items_inventory_stage",
        key: "total_add_items_inventory_stage",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_add_items_inventory_stage > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# of Items Additionally Scanned",
        dataIndex: "total_add_items_to_inventory_scan",
        key: "total_add_items_to_inventory_scan",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_add_items_to_inventory_scan > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# Of Items Added To Storage",
        dataIndex: "total_add_items_storage_stage",
        key: "total_add_items_storage_stage",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_add_items_storage_stage > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# Of Items Removed From Storage",
        dataIndex: "total_remove_items_storage_stage",
        key: "total_remove_items_storage_stage",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_remove_items_storage_stage > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# Of Items Removed From Inventory",
        dataIndex: "total_remove_items_inventory_stage",
        key: "total_remove_items_inventory_stage",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_remove_items_inventory_stage > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },

      {
        title: "# of Items Additionally Scanned",
        dataIndex: "total_remove_items_to_inventory_scan",
        key: "total_remove_items_to_inventory_scan",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_remove_items_to_inventory_scan > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },

      {
        title: "Total # Of Items In Inventory",
        dataIndex: "total_inventory_items_previous_stage",
        key: "total_inventory_items_previous_stage",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const calculatedValue =
            record.total_inventory_items_previous_stage -
            record.total_remove_items_inventory_previous_stage;
          return (
            <span
              className="table-cell-font"
              style={{ fontWeight: calculatedValue > 0 ? "bold" : "inherit" }}
            >
              {calculatedValue}
            </span>
          );
        },
      },
      {
        title: "Total # Of Items In Storage",
        dataIndex: "total_storage_items_previous_stage",
        key: "total_storage_items_previous_stage",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const calculatedValue =
            record.total_storage_items_previous_stage -
            record.total_remove_items_storage_previous_stage;
          return (
            <span
              className="table-cell-font"
              style={{ fontWeight: calculatedValue > 0 ? "bold" : "inherit" }}
            >
              {calculatedValue}
            </span>
          );
        },
      },
      {
        title: "Total Weight(lbs) In Inventory",
        dataIndex: "total_inventory_items_previous_stage_weight",
        key: "total_inventory_items_previous_stage_weight",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const calculatedValue =
            record.total_inventory_items_previous_stage_weight -
            record.total_remove_inventory_items_previous_stage_weight;
          return (
            <span
              className="table-cell-font"
              style={{ fontWeight: calculatedValue > 0 ? "bold" : "inherit" }}
            >
              {calculatedValue}
            </span>
          );
        },
      },
      {
        title: "Total Weight(lbs) In Storage",
        dataIndex: "total_storage_items_previous_stage_weight",
        key: "total_storage_items_previous_stage_weight",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const calculatedValue =
            record.total_storage_items_previous_stage_weight -
            record.total_remove_storage_items_previous_stage_weight;
          return (
            <span
              className="table-cell-font"
              style={{ fontWeight: calculatedValue > 0 ? "bold" : "inherit" }}
            >
              {calculatedValue}
            </span>
          );
        },
      },
      {
        title: "Delta(Items In Inventory - Items In Storage)",
        dataIndex: "grand_totel",
        key: "grand_totel",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const totalInventoryItem =
            record.total_inventory_items_previous_stage -
            record.total_remove_items_inventory_previous_stage;
          const ifAddItemToInventory =
            record.total_inventory_items_previous_stage -
            record.total_storage_items_previous_stage;
          const ifRemoveFromStorage =
            record.total_storage_items_previous_stage -
            record.total_remove_items_storage_previous_stage;
          if (record.add_items_to_inventory) {
            return (
              <span
                className="table-cell-font"
                style={{
                  fontWeight: ifAddItemToInventory > 0 ? "bold" : "inherit",
                }}
              >
                {ifAddItemToInventory}
              </span>
            );
          } else if (record.total_add_items_to_inventory_scan) {
            return (
              <span
                className="table-cell-font"
                style={{
                  fontWeight: ifAddItemToInventory > 0 ? "bold" : "inherit",
                }}
              >
                {ifAddItemToInventory}
              </span>
            );
          } else if (record.total_remove_items_to_inventory_scan) {
            return (
              <span
                className="table-cell-font"
                style={{
                  fontWeight: ifAddItemToInventory > 0 ? "bold" : "inherit",
                }}
              >
                {ifAddItemToInventory}
              </span>
            );
          } else if (record.assign_storage_units_to_items) {
            return (
              <span
                className="table-cell-font"
                style={{
                  fontWeight: ifAddItemToInventory > 0 ? "bold" : "inherit",
                }}
              >
                {ifAddItemToInventory}
              </span>
            );
          } else if (record.unassign_storage_units_from_items) {
            return (
              <span
                className="table-cell-font"
                style={{
                  fontWeight:
                    totalInventoryItem > 0
                      ? totalInventoryItem - ifRemoveFromStorage > 0
                        ? "bold"
                        : "inherit"
                      : "inherit",
                }}
              >
                {totalInventoryItem > 0
                  ? totalInventoryItem - ifRemoveFromStorage
                  : 0}
              </span>
            );
          } else if (record.remove_items_to_inventory) {
            return (
              <span
                className="table-cell-font"
                style={{
                  fontWeight: totalInventoryItem > 0 ? "bold" : "inherit",
                }}
              >
                {totalInventoryItem}
              </span>
            );
          } else {
            return "-";
          }
        },
      },
    ];

    const inventoryColumn = [
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        width: "5%",
        align: "center",
        render: (text, record) => {
          return (
            <div className="table-cell-font" style={{ textAlign: "left" }}>
              {" "}
              {record.order_of_stages
                ? `${record.order_of_stages} - ${record.name}`
                : record.name}{" "}
            </div>
          );
        },
      },
      {
        title: "Date Completed",
        dataIndex: "created_at",
        key: "created_at",
        width: "5%",
        align: "center",
        render: (text, record) => {
          return (
            <div className="table-cell-font">
              {" "}
              {record.created_at ? this.toDate(record.created_at) : "-"}{" "}
            </div>
          );
        },
      },
      {
        title: "# Of Items Added To Inventory",
        dataIndex: "total_add_items_inventory_stage",
        key: "total_add_items_inventory_stage",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_add_items_inventory_stage > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# of Items Additionally Scanned",
        dataIndex: "total_add_items_to_inventory_scan",
        key: "total_add_items_to_inventory_scan",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_add_items_to_inventory_scan > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# Of Items Removed From Inventory",
        dataIndex: "total_remove_items_inventory_stage",
        key: "total_remove_items_inventory_stage",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_remove_items_inventory_stage > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },
      {
        title: "# of Items Additionally Scanned",
        dataIndex: "total_remove_items_to_inventory_scan",
        key: "total_remove_items_to_inventory_scan",
        width: "5%",
        align: "center",
        render: (text, record) => ({
          props: {
            className:
              !record.isLast && record.total_remove_items_to_inventory_scan > 0
                ? "active-text"
                : "",
          },
          children: <div className="table-cell-font">{text}</div>,
        }),
      },

      {
        title: "Total # Of Items In Inventory",
        dataIndex: "total_inventory_items_previous_stage",
        key: "total_inventory_items_previous_stage",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const calculatedValue =
            record.total_inventory_items_previous_stage -
            record.total_remove_items_inventory_previous_stage;
          return (
            <span
              className="table-cell-font"
              style={{ fontWeight: calculatedValue > 0 ? "bold" : "inherit" }}
            >
              {calculatedValue}
            </span>
          );
        },
      },
      {
        title: "Total Weight(lbs) In Inventory",
        dataIndex: "total_items_weight_in_shipment",
        key: "total_items_weight_in_shipment",
        width: "5%",
        align: "center",
        render: (text, record) => {
          const calculatedValue = record.total_items_weight_in_shipment
            ? Math.round(record.total_items_weight_in_shipment)
            : 0;
          return (
            <span
              className="table-cell-font"
              style={{ fontWeight: calculatedValue > 0 ? "bold" : "inherit" }}
            >
              {calculatedValue}
            </span>
          );
        },
      },
    ];

    const columns2 = [
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
        sorter: true,
      },
      {
        title: "Order of stage",
        dataIndex: "order_of_stages",
        key: "order_of_stages",
        sorter: true,
      },
      {
        title: "Add Items To Inventory",
        dataIndex: "add_items_to_inventory",
        key: "add_items_to_inventory",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.add_items_to_inventory === "1" ||
                record.add_items_to_inventory === 1 ||
                record.add_items_to_inventory === true ||
                record.add_items_to_inventory === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Additional Scan",
        dataIndex: "scan_require",
        key: "scan_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.scan_require === "1" ||
                record.scan_require === 1 ||
                record.scan_require === true ||
                record.scan_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Remove Items From Inventory",
        dataIndex: "remove_items_to_inventory",
        key: "remove_items_to_inventory",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.remove_items_to_inventory === "1" ||
                record.remove_items_to_inventory === 1 ||
                record.remove_items_to_inventory === true ||
                record.remove_items_to_inventory === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Additional Scan",
        dataIndex: "remove_scan_require",
        key: "remove_scan_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.remove_scan_require === "1" ||
                record.remove_scan_require === 1 ||
                record.remove_scan_require === true ||
                record.remove_scan_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },

      {
        title: "Enable Partial Complete Stage",
        dataIndex: "enable_partial_complete_stage",
        key: "enable_partial_complete_stage",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.enable_partial_complete_stage === "1" ||
                record.enable_partial_complete_stage === 1 ||
                record.enable_partial_complete_stage === true ||
                record.enable_partial_complete_stage === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Carrier Signature Required",
        dataIndex: "supervisor_signature_require",
        key: "supervisor_signature_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.supervisor_signature_require === "1" ||
                record.supervisor_signature_require === 1 ||
                record.supervisor_signature_require === true ||
                record.supervisor_signature_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Customer Signature Required",
        dataIndex: "customer_signature_require",
        key: "customer_signature_require",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {record.customer_signature_require === "1" ||
                record.customer_signature_require === 1 ||
                record.customer_signature_require === true ||
                record.customer_signature_require === "yes"
                ? "Yes"
                : "No"}
            </div>
          );
        },
      },
      {
        title: "Created Date",
        dataIndex: "created_at",
        key: "created_at",
        align: "center",
        sorter: true,
        render: (text, record) => {
          return (
            <div>
              {" "}
              {record.date ? DateFns.format(record.date, "MM/DD/YYYY") : ""}
            </div>
          );
        },
      },
      {
        title: "Action",
        key: "action",
        align: "center",
        render: (record, text) => {
          return (
            <div className="icons">
              {text.order_of_stages > this.state.current_job_stage_check ? (
                <>
                  <Tooltip title="View">
                    <Button
                      type="primary"
                      className="c-btn c-round c-success"
                      icon="eye"
                      onClick={() =>
                        this.viewShipmentStage(
                          text.local_shipment_stage_id,
                          this.state.inventoryData
                        )
                      }
                    ></Button>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <Button
                      type="primary"
                      className="c-btn c-round c-warning"
                      icon="edit"
                      onClick={() =>
                        this.editShipmentStage(
                          text.local_shipment_stage_id,
                          this.state.inventoryData
                        )
                      }
                    ></Button>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <Button
                      type="primary"
                      className="c-btn c-round c-danger"
                      icon="delete"
                      onClick={() => this.deleteShipmentStage(text)}
                      disabled={
                        text.local_shipment_stage_id <
                        (this.state.current_job_stage_check &&
                          this.state.current_job_stage_check)
                      }
                    ></Button>
                  </Tooltip>
                </>
              ) : (
                <Tooltip title="View">
                  <Button
                    type="primary"
                    className="c-btn c-round c-success"
                    icon="eye"
                    onClick={() =>
                      this.viewShipmentStage(
                        text.local_shipment_stage_id,
                        this.state.inventoryData
                      )
                    }
                  ></Button>
                </Tooltip>
              )}
            </div>
          );
        },
      },
    ];

    const content = (
      <SortMenu>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Label number", "qr_id", "ASC")}
        >
          Label Number: Low to high
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Label number",
            "qr_id",
            "DESC"
          )}
        >
          Label Number: high to Low
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Scanned Items",
            "isScannedFlag",
            "DESC"
          )}
        >
          Scanned Items
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Unscanned Items",
            "isScannedFlag",
            "ASC"
          )}
        >
          Unscanned Items
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Volume", "volume", "DESC")}
        >
          Volume: High to low
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Volume", "volume", "ASC")}
        >
          Volume: Low to high
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Weight", "weight", "DESC")}
        >
          Weight: High to low
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Weight", "weight", "ASC")}
        >
          Weight: Low to high
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Alphabetical",
            "item_name",
            "ASC"
          )}
        >
          Alphabetical : A to Z
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Alphabetical",
            "item_name",
            "DESC"
          )}
        >
          Alphabetical : Z to A
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Order of Creation",
            "created_at",
            "ASC"
          )}
        >
          Order of Creation : First-Last
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Order of Creation",
            "created_at",
            "DESC"
          )}
        >
          Order of Creation : Last-First
        </p>
        <p>
          <button style={{ color: "black" }} onClick={this.hide}>
            Close
          </button>
        </p>
      </SortMenu>
    );

    return (
      <LayoutContentWrapper>
        <div className="add_header">
          <h2 style={{ marginBottom: "0" }}>
            <i className="fas fa-edit" />
            &emsp;View Shipment
          </h2>
          <button
            className="backButton"
            onClick={() => this.props.history.goBack()}
          >
            <i className="fa fa-chevron-left" aria-hidden="true" /> Back
          </button>
        </div>
        <LayoutContent>
          <div>
            <Spin spinning={this.state.contentloader} indicator={antIcon}>
              <Spin
                spinning={this.state.contentloaderInventory}
                indicator={antIcon}
              >
                <Dropdown overlay={menu}>
                  <Button type="primary">
                    Print <Icon type="down" />
                  </Button>
                </Dropdown>

                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Shipment Documents </h2>
                    </label>
                  </div>
                </Divider>

                <div>
                  <div>
                    <label
                      style={{
                        padding: "10px",
                        borderRadius: "5px",
                        backgroundColor: "#f9f9f9",
                        color: "#333",
                        fontSize: "16px",
                        display: "inline-block",
                        cursor: "pointer",
                        color: this.state.isHovered ? "#1890FF" : "#333",
                        border: this.state.isHovered
                          ? "1px solid #1890FF"
                          : "1px solid #ccc",
                      }}
                      onMouseEnter={() => this.handleHover(true)}
                      onMouseLeave={() => this.handleHover(false)}
                    >
                      <Icon type="upload" /> Upload Files
                      <input
                        type="file"
                        style={{ display: "none" }}
                        multiple
                        onChange={this.handleFileChange}
                      />
                    </label>
                  </div>
                </div>

                <ImagesContainer>
                  <Row gutter={[16, 16]}>
                    {this.state.pdfList && this.state.pdfList.length > 0
                      ? this.state.pdfList.map((data, index) => (
                        <Col
                          xl={4}
                          lg={6}
                          md={8}
                          sm={12}
                          xs={24}
                          className="main-images"
                          key={index}
                        >
                          <Card
                            style={{ height: "100%" }}
                            cover={
                              <img
                                alt="example"
                                src="https://st2.depositphotos.com/1431107/10831/v/950/depositphotos_108313632-stock-illustration-download-pdf-file-button.jpg"
                              />
                            }
                            actions={[
                              <Button
                                type="primary"
                                className="c-btn c-round c-success"
                                icon="printer"
                                onClick={() =>
                                  this.downloadPdf(data.PDF_URL, data.name)
                                }
                              ></Button>,
                              <Button
                                type="primary"
                                className="c-btn c-round c-danger"
                                icon="delete"
                                onClick={() =>
                                  this.deletePDF(
                                    data.shipment_job_document_id
                                  )
                                }
                              ></Button>,
                            ]}
                          >
                            <Meta
                              description={
                                <div
                                  style={{
                                    height: "40px",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                  }}
                                >
                                  {data.name.length > MAX_DESCRIPTION_LENGTH
                                    ? `${data.name.substring(
                                      0,
                                      MAX_DESCRIPTION_LENGTH
                                    )}...`
                                    : data.name}
                                </div>
                              }
                            />
                          </Card>
                        </Col>
                      ))
                      : null}
                  </Row>
                </ImagesContainer>

                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Shipment Details </h2>
                    </label>
                  </div>
                </Divider>
                <Row style={{ textTransform: "capitalize" }}>
                  <Col span={12}>
                    <div className="display-fle">
                      <label className="fs-16 medium-text">
                        <h3>Shipment Name:</h3>
                      </label>
                      <div>
                        <h3>{this.state.inventoryData.shipment_name}</h3>
                      </div>
                    </div>

                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Shipment Number:</h3>
                      </label>
                      <div>
                        <h3>{`#${this.state.inventoryData.job_number
                          ? this.state.inventoryData.job_number
                          : ""
                          }`}</h3>
                      </div>
                    </div>

                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>warehouse Name:</h3>
                      </label>
                      <div>
                        {!this.state.isMainAdmin ? (
                          this.state.warehousesList.map((data, index) => {
                            if (data.id === this.state.warehouseId) {
                              return <h3 key={index}>{data.name}</h3>;
                            }
                            return null;
                          })
                        ) : (
                          <h3> {this.state.warehouseNameMainAdmin} </h3>
                        )}
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="display-fle x">
                      <label className="fs-16 medium-text">
                        <h3>Created By:</h3>
                      </label>
                      <div>
                        <h3>{this.state.inventoryData.created_by_name}</h3>
                      </div>
                    </div>

                    <div className="display-fle x">
                      <label className="fs-16 medium-text">
                        <h3>Created On:</h3>
                      </label>
                      <div>
                        <h3>
                          {this.toDate(this.state.inventoryData.created_at)}
                        </h3>
                      </div>
                    </div>
                  </Col>
                </Row>
                <div>
                  <Divider orientation="center" typcurrente="horizontal">
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h2>Customer Info </h2>
                      </label>
                    </div>
                  </Divider>

                  <Dropdown overlay={customerManu}>
                    <Button type="primary">
                      Send Job Summary Email <Icon type="down" />
                    </Button>
                  </Dropdown>

                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h3>Customer:&nbsp;&nbsp;</h3>
                    </label>
                    <div>
                      <h3>
                        {this.state.inventoryData.customer_job &&
                          `${this.state.inventoryData.customer_job.first_name
                          } ${this.state.inventoryData.customer_job.last_name
                            ? this.state.inventoryData.customer_job.last_name
                            : ""
                          }`}
                      </h3>
                    </div>
                  </div>
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h3>Contact:&nbsp;&nbsp;</h3>
                    </label>
                    <h3>
                      <NumberFormat
                        value={
                          this.state.inventoryData.customer_job &&
                            this.state.inventoryData.customer_job.phone &&
                            this.state.inventoryData.customer_job.phone !==
                            "undefined"
                            ? this.state.inventoryData.customer_job.phone
                            : ""
                        }
                        displayType={"text"}
                        format="(###) ###-####"
                      />
                    </h3>
                  </div>
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h3>Email:&nbsp;&nbsp;</h3>
                    </label>
                    <h3>
                      {this.state.inventoryData.customer_job &&
                        this.state.inventoryData.customer_job.email}
                    </h3>
                  </div>
                </div>
                <div>
                  <Divider orientation="center" type="horizontal">
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h2>Activity History</h2>
                      </label>
                    </div>
                  </Divider>

                  <div style={{ marginTop: "10px", overflowX: "auto" }}>
                    <Table
                      bordered={true}
                      columns={
                        this.state.isMainAdmin
                          ? storageColumn
                          : this.state.integrationKeyStatus &&
                            this.state.inventoryData.storage_shipment_job_id !==
                            null &&
                            this.state.inventoryData.storage_shipment_job_id !==
                            "" &&
                            this.state.inventoryData.storage_shipment_job_id !==
                            undefined
                            ? storageColumn
                            : inventoryColumn
                      }
                      dataSource={dataSource}
                      onChange={this.handleChange}
                      pagination={false}
                      border={true}
                      className={
                        this.state.integrationKeyStatus &&
                          this.state.inventoryData.storage_shipment_job_id !==
                          null &&
                          this.state.inventoryData.storage_shipment_job_id !==
                          "" &&
                          this.state.inventoryData.storage_shipment_job_id !==
                          undefined
                          ? "storageColumnCustomTable"
                          : "inventoryColumnCustomTable"
                      }
                      rowClassName={(record) =>
                        record.isLast ? "last-row" : ""
                      }
                      cellF
                    />
                  </div>
                </div>

                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Origin and Destination </h2>
                    </label>
                  </div>
                </Divider>
                <Row>
                  <Col span={12}>
                    <Div>
                      <div className="origin">
                        <label className="fs-16 medium-text">
                          <h3>Origin </h3>
                        </label>

                        <div style={{ display: "flex" }}>
                          <p>
                            {this.state.inventoryData &&
                              this.state.inventoryData.pickup_address &&
                              this.state.inventoryData.pickup_address !==
                              "undefined"
                              ? this.state.inventoryData.pickup_address
                              : ""}
                          </p>
                          &nbsp;
                          <p>
                            {this.state.inventoryData &&
                              this.state.inventoryData.pickup_address2 &&
                              this.state.inventoryData.pickup_address2 !==
                              "undefined"
                              ? this.state.inventoryData.pickup_address2
                              : ""}
                          </p>
                        </div>
                        <div style={{ display: "flex" }}>
                          <p>
                            {this.state.inventoryData &&
                              this.state.inventoryData.pickup_city &&
                              this.state.inventoryData.pickup_city !== "undefined"
                              ? this.state.inventoryData.pickup_city
                              : ""}
                          </p>
                          <p>
                            {this.state.inventoryData &&
                              this.state.inventoryData.pickup_state &&
                              this.state.inventoryData.pickup_state !==
                              "undefined"
                              ? ", " + this.state.inventoryData.pickup_state
                              : ""}
                          </p>
                          &nbsp;&nbsp;
                          <p>
                            {this.state.inventoryData &&
                              this.state.inventoryData.pickup_zipcode &&
                              this.state.inventoryData.pickup_zipcode !==
                              "undefined"
                              ? this.state.inventoryData.pickup_zipcode
                              : ""}
                          </p>
                        </div>
                        <div>
                          {this.state.inventoryData &&
                            this.state.inventoryData.pickup_country &&
                            this.state.inventoryData.pickup_country !==
                            "undefined"
                            ? this.state.inventoryData.pickup_country
                            : ""}
                        </div>
                      </div>
                    </Div>
                  </Col>
                  <Col span={12}>
                    <Div>
                      <div className="origin">
                        <label className="fs-16 medium-text">
                          <h3>Destination </h3>
                        </label>
                        <div style={{ display: "flex" }}>
                          <p>{this.state.inventoryData.delivery_address}</p>
                          &nbsp;
                          <p>
                            {this.state.inventoryData.delivery_address2
                              ? this.state.inventoryData.delivery_address2
                              : ""}
                          </p>
                        </div>
                        <div style={{ display: "flex" }}>
                          <p>{this.state.inventoryData.delivery_city}</p>
                          <p>
                            {this.state.inventoryData.delivery_state
                              ? `, ${this.state.inventoryData.delivery_state}`
                              : this.state.inventoryData.delivery_state}
                          </p>
                          &nbsp;&nbsp;
                          <p>{this.state.inventoryData.delivery_zipcode}</p>
                        </div>
                        <div>{this.state.inventoryData.delivery_country}</div>
                      </div>
                    </Div>
                  </Col>
                </Row>
                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Shipment Summary</h2>
                    </label>
                  </div>
                </Divider>
                <Row style={{ textTransform: "capitalize" }}>
                  <Col span={12}>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Total items:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>{this.state.inventoryData.total_items}</h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Total volume:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {Math.round(this.state.inventoryData.total_volume)} Cu
                        Ft
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Disassembled Items:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData &&
                          this.state.inventoryData.total_disassembled_items
                          ? this.state.inventoryData.total_disassembled_items
                          : "0"}
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>High Value Items:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData &&
                          this.state.inventoryData.total_highValue_items
                          ? this.state.inventoryData.total_highValue_items
                          : "0"}
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Pro gear items:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData &&
                          this.state.inventoryData.total_is_pro_gear_items
                          ? this.state.inventoryData.total_is_pro_gear_items
                          : "0"}
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Items with exceptions:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.data
                          ? this.countException(this.state.data)
                          : "0"}
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Supervisor:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData &&
                          this.state.inventoryData.assign_worker
                          ? this.state.inventoryData.assign_worker.map(
                            (worker) => {
                              if (worker.role === "supervisor") {
                                return (
                                  worker.first_name +
                                  " " +
                                  worker.last_name +
                                  "," +
                                  " "
                                );
                              }
                              return null;
                            }
                          )
                          : ""}
                      </h3>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Total Cartons:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>{`${this.state.inventoryData.total_cartons} (CP ${this.state.inventoryData.total_cartons_cp}, PBO ${this.state.inventoryData.total_cartons_pbo})`}</h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Total Weight:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>{this.state.inventoryData.total_weight} Lbs</h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Electronics:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData &&
                          this.state.inventoryData.total_electronics_items
                          ? this.state.inventoryData.total_electronics_items
                          : "0"}
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>High Value total:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        <NumberFormat
                          value={this.state.inventoryData.total_high_value}
                          displayType={"text"}
                          thousandSeparator={true}
                          prefix={"$"}
                        />
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Pro gear weight:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData.total_pro_gear_weight} Lbs
                      </h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Pads Used:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>{this.state.inventoryData.total_pads_used}</h3>
                    </div>
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h3>Workers:&nbsp;&nbsp;</h3>
                      </label>
                      <h3>
                        {this.state.inventoryData &&
                          this.state.inventoryData.assign_worker
                          ? this.state.inventoryData.assign_worker.map(
                            (worker) => {
                              if (worker.role === "worker") {
                                return (
                                  worker.first_name +
                                  " " +
                                  worker.last_name +
                                  "," +
                                  " "
                                );
                              }
                              return null;
                            }
                          )
                          : ""}
                      </h3>
                    </div>
                  </Col>
                </Row>
                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Items</h2>
                    </label>
                  </div>
                </Divider>
                <SortContainer>
                  <div className="sort-dropdown">
                    <Popover
                      content={content}
                      title="Title"
                      trigger="click"
                      visible={this.state.visible}
                      onVisibleChange={this.handleVisibleChange}
                    >
                      <button>
                        Sort by : {this.state.sort_field}
                        <i className="fa fa-caret-down"></i>
                      </button>
                    </Popover>
                  </div>
                  <div
                    style={{
                      minWidth: "200px",
                      maxWidth: "400px",
                      marginTop: "5px",
                    }}
                  >
                    <Select
                      getPopupContainer={(trigger) => trigger.parentNode}
                      mode="multiple"
                      labelInValue
                      allowClear
                      style={{
                        width: "100%",
                        maxHeight: "120px",
                        overflow: "auto",
                        border: "1px solid #d9d9d9",
                        scrollbarWidth: "thin",
                        scrollbarColor: "#888 transparent",
                        padding: "0px",
                        margin: "0px",
                        borderRadius: "5px",
                      }}
                      placeholder="Please select tags"
                      onChange={this.handleChangeJobItemTagList}
                    >
                      {this.state.jobItemTagList.map((item, index) => (
                        <Option value={item.tag_id} key={item.tag_id}>
                          <Tag color={item.color ? item.color : ""}>
                            {item.name}
                          </Tag>
                        </Option>
                      ))}
                    </Select>
                  </div>
                  <div
                    style={{
                      minWidth: "200px",
                      maxWidth: "400px",
                      marginLeft: "15px",
                      marginTop: "5px",
                    }}
                  >
                    <Select
                      getPopupContainer={(trigger) => trigger.parentNode}
                      mode="multiple"
                      labelInValue
                      allowClear
                      style={{
                        width: "100%",
                        maxHeight: "120px",
                        overflow: "auto",
                        border: "1px solid #d9d9d9",
                        scrollbarWidth: "thin",
                        scrollbarColor: "#888 transparent",
                        padding: "0px",
                        margin: "0px",
                        borderRadius: "5px",
                      }}
                      placeholder="Please select rooms"
                      onChange={this.handleChangeJobItemRoomList}
                    >
                      {this.state.jobItemRoomList.map((item, index) => (
                        <Option value={item.room_id} key={item.room_id}>
                          {item.room_name}
                        </Option>
                      ))}
                    </Select>
                  </div>
                  <div className="moving-sort-button-container">
                    <Button
                      className={
                        this.state.active === 1 ? "active-btn" : "normal-btn"
                      }
                      onClick={this.allClickAPI}
                    >
                      All
                    </Button>
                    {this.state.roomList.length
                      ? this.state.roomList.map((item, index) => (
                        <Button
                          className={
                            this.state.active === index + 2
                              ? "active-btn"
                              : "normal-btn"
                          }
                          onClick={this.roomClikAPI.bind(null, index + 2)}
                          value={item.shipment_room_id}
                        >
                          {item.name}
                        </Button>
                      ))
                      : ""}
                  </div>
                  <div style={{ marginLeft: "15px" }}>
                    <Search
                      placeholder="Search Items"
                      onChange={(e) => this.handleSearch(e.target.value)}
                      value={this.state.search}
                      style={{ width: 200 }}
                    />
                  </div>
                </SortContainer>

                <Tabs
                  defaultActiveKey={this.state.defaultTabActiveKey}
                  onChange={this.tabChangeHandler}
                >
                  {this.state &&
                    this.state.shipmentStagesTab &&
                    this.state.shipmentStagesTab.map((result, i) => (
                      <TabPane tab={result.name} key={i}>
                        <ImagesContainer>
                          <Spin
                            spinning={this.state.imageItemLoading}
                            indicator={antIcon}
                          >
                            <Row>
                              {newArray.length > 0
                                ? newArray.map((data, index) => (
                                  <Col
                                    xl={4}
                                    lg={6}
                                    md={8}
                                    sm={6}
                                    className="main-images"
                                    key={index}
                                  >
                                    <div
                                      className="inner-item"
                                      style={{
                                        cursor: "pointer",
                                        overflow: "clip",
                                      }}
                                      onClick={() =>
                                        this.viewInventory(data.inventory_id)
                                      }
                                    >
                                      <div
                                        class="overlay"
                                        style={{
                                          position: "absolute",
                                          top: 0,
                                          left: 0,
                                          width: "100%",
                                          height: "100%",
                                          backgroundColor:
                                            "rgba(0, 0, 0, 0.5)",
                                          opacity: getOverlayOpacity(data),
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "center",
                                          zIndex: 100,
                                          transition: "opacity 0.3s ease",
                                        }}
                                      >
                                        <Icon
                                          style={{
                                            color: "#ffffff",
                                            fontSize: "50px",
                                          }}
                                          type="check"
                                        />
                                      </div>
                                      <div
                                        style={{
                                          height: "30px",
                                          fontSize: "12px",
                                          textAlign: "center",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                        }}
                                      >
                                        {data.item_name.length > 15
                                          ? `${data.item_name.substring(
                                            0,
                                            15
                                          )}...`
                                          : data.item_name}
                                      </div>
                                      <div className="image-container">
                                        <img
                                          src={
                                            data.item_photos &&
                                              data.item_photos.some(
                                                (photo) =>
                                                  photo.is_thumbnail === 1
                                              )
                                              ? data.item_photos.find(
                                                (photo) =>
                                                  photo.is_thumbnail === 1
                                              ).item_photo
                                              : data.item_photos &&
                                                data.item_photos.length > 0
                                                ? data.item_photos[0].item_photo
                                                : smallImage
                                          }
                                          alt="..."
                                          className="small-img"
                                        />
                                        {data.item_photos.length > 1 ? (
                                          <span className="extra-image">
                                            +{data.item_photos.length - 1}
                                          </span>
                                        ) : (
                                          ""
                                        )}
                                        {data.exceptions.length >= 1 ? (
                                          <span className="exception">
                                            {data.exceptions.length > 1
                                              ? `+${data.exceptions.length -
                                              1} `
                                              : ""}
                                            E
                                          </span>
                                        ) : (
                                          ""
                                        )}
                                        {data.isOverride === "yes" ? (
                                          <span className="override">O</span>
                                        ) : (
                                          ""
                                        )}
                                      </div>
                                      <div
                                        style={{
                                          height: "40px",
                                          fontSize: "13px",
                                          textAlign: "center",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                        }}
                                      >
                                        {data.volume +
                                          " " +
                                          "Cu. Ft. /" +
                                          data.weight +
                                          " " +
                                          "Lbs"}
                                      </div>
                                      <div
                                        style={{
                                          height: "40px",
                                          fontSize: "13px",
                                          textAlign: "center",
                                          color: "#8ab932",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                        }}
                                      >
                                        {data.type === "Generic"
                                          ? "G-"
                                          : data.type === "External"
                                            ? "E-"
                                            : ""}
                                        {data.item_qr.random_number.length >
                                          10
                                          ? data.item_qr.label_number +
                                          "/" +
                                          data.item_qr.random_number.substring(
                                            0,
                                            10
                                          ) +
                                          "..."
                                          : data.item_qr.label_number +
                                          "/" +
                                          data.item_qr.random_number}
                                      </div>

                                      {data.unit_list !== "" &&
                                        data.unit_list !== undefined &&
                                        data.unit_list !== null ? (
                                        <div
                                          style={{
                                            height: "30px",
                                            fontSize: "13px",
                                            textAlign: "center",
                                            color: "#8ab932",
                                            overflow: "hidden",
                                            textOverflow: "ellipsis",
                                          }}
                                        >
                                          {data.unit_list.currentLocation
                                            .length > 12
                                            ? data.unit_list.name +
                                            "/" +
                                            data.unit_list.currentLocation.substring(
                                              0,
                                              12
                                            ) +
                                            "..."
                                            : data.unit_list.name +
                                            "/" +
                                            data.unit_list.currentLocation}
                                        </div>
                                      ) : (
                                        ""
                                      )}
                                    </div>
                                  </Col>
                                ))
                                : null}
                            </Row>
                            <Pagination
                              pageSize={pageSize}
                              current={this.state.current}
                              total={this.state.totalCount}
                              onChange={this.handleChangePagination}
                              style={{ bottom: "0px" }}
                            />
                          </Spin>
                        </ImagesContainer>
                      </TabPane>
                    ))}
                </Tabs>

                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Unit List</h2>
                    </label>
                  </div>
                </Divider>
                <Row style={{ textTransform: "capitalize" }}>
                  <Row>
                    {this.state.unitList.length > 0
                      ? this.state.unitList.length == 1
                        ? this.state.unitList.map((data, index) => {
                          return (
                            <span key={index} style={{ fontSize: "20px" }}>
                              {data.name}{" "}
                            </span>
                          );
                        })
                        : this.state.unitList.map((data, index) => {
                          return (
                            <span
                              style={{ marginRight: "5px", fontSize: "20px" }}
                              key={index}
                            >
                              {data.name + " " + "|"}
                            </span>
                          );
                        })
                      : null}
                  </Row>
                </Row>
                <Divider orientation="center" type="horizontal">
                  <div className="display-flex">
                    <label className="fs-16 medium-text">
                      <h2>Signature History</h2>
                    </label>
                  </div>
                </Divider>

                <Row style={{ textTransform: "capitalize" }}>
                  <Col span={24}>
                    <Signature>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "normal",
                          padding: "0px 20px",
                        }}
                      >
                        <div style={{ width: "200px", padding: "5px" }}>
                          <p style={{ textAlign: "left" }}>
                            <strong>Stage</strong>
                          </p>
                        </div>
                        <div style={{ width: "350px", padding: "5px" }}>
                          <p style={{ textAlign: "center" }}>
                            <strong>User</strong>
                          </p>
                        </div>
                        <div style={{ width: "350px", padding: "5px" }}>
                          <p style={{ textAlign: "center" }}>
                            <strong>Customer</strong>
                          </p>
                        </div>
                        <div style={{ width: "200px", padding: "5px" }}>
                          <p style={{ textAlign: "right" }}>
                            <strong>Date/Time</strong>
                          </p>
                        </div>
                      </div>

                      {this.state.inventoryData.shipment_type_for_shipment &&
                        this.state.inventoryData.shipment_type_for_shipment
                          .local_shipment_stage.length >= 1 ? (
                        this.state.inventoryData.shipment_type_for_shipment.local_shipment_stage.map(
                          (e, index) => {
                            return (
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "normal",
                                  padding: "10px 0px",
                                }}
                              >
                                <div
                                  style={{
                                    justifySelf: "baseline",
                                    width: "200px",
                                    padding: "10px",
                                    alignItems: "left",
                                  }}
                                >
                                  <p>Stage {index + 1}:- </p>
                                  <p>{e.name}</p>
                                </div>
                                <div
                                  style={{
                                    width: "350px",
                                    padding: "8px",
                                    alignItems: "center",
                                  }}
                                >
                                  {e.supervisor_signature ? (
                                    <>
                                      <p style={{ textAlign: "center" }}>
                                        {e.supervisor_name}
                                      </p>
                                      <img
                                        style={{ marginLeft: "70px" }}
                                        src={e.supervisor_signature}
                                        alt=""
                                        width="125px"
                                      />
                                      <p>
                                        {
                                          e.why_supervisor_signature_require_note
                                        }
                                      </p>
                                    </>
                                  ) : (
                                    <Icon
                                      style={{
                                        fontSize: "20px",
                                        marginLeft: "120px",
                                      }}
                                      type="minus"
                                    />
                                  )}
                                </div>

                                <div
                                  style={{
                                    width: "350px",
                                    padding: "8px",
                                    alignItems: "center",
                                  }}
                                >
                                  {e.customer_signature ? (
                                    <>
                                      <p style={{ textAlign: "center" }}>
                                        {e.customer_name}
                                      </p>

                                      <img
                                        style={{ marginLeft: "70px" }}
                                        src={e.customer_signature}
                                        alt=""
                                        width="125px"
                                      />
                                      <p>
                                        {e.why_customer_signature_require_note}
                                      </p>
                                    </>
                                  ) : (
                                    <Icon
                                      style={{
                                        fontSize: "20px",
                                        marginLeft: "120px",
                                      }}
                                      type="minus"
                                    />
                                  )}
                                </div>
                                <p
                                  style={{
                                    width: "200px",
                                    padding: "5px",
                                    alignItems: "right",
                                  }}
                                >
                                  {e.created_at ? (
                                    this.toDate(e.created_at)
                                  ) : (
                                    <Icon
                                      style={{
                                        fontSize: "20px",
                                        marginLeft: "120px",
                                      }}
                                      type="minus"
                                    />
                                  )}
                                </p>
                              </div>
                            );
                          }
                        )
                      ) : (
                        <h2 className="no_signature">
                          <strong>No Signature history!</strong>
                        </h2>
                      )}
                    </Signature>
                  </Col>
                </Row>

                <div>
                  <Divider orientation="center" type="horizontal">
                    <div className="display-flex">
                      <label className="fs-16 medium-text">
                        <h2>Shipment Settings</h2>
                      </label>
                    </div>
                  </Divider>
                  <div
                    className="display-flex"
                    style={{ alignItems: "baseline" }}
                  >
                    <label className="fs-16 medium-text">
                      <h3>Current Shipment status:&nbsp;&nbsp;</h3>
                    </label>
                    {this.state.isJobComplete === 1 ? (
                      <h3 style={{ color: "green" }}> Shipment Completed </h3>
                    ) : (
                      <>
                        {this.state.shipmentStage.length > 0 &&
                          this.state.shipmentStage.map((result, i) => {
                            if (
                              result.local_shipment_stage_id ===
                              this.state.inventoryData.local_job_status
                            ) {
                              return <h3 key={i}>{result.name}</h3>;
                            }
                            return null;
                          })}
                        &nbsp;
                        <h4>
                          (Stage&nbsp;
                          {this.state.inventoryData &&
                            this.state.inventoryData.shipment_type_for_shipment &&
                            this.state.inventoryData.shipment_type_for_shipment
                              .current_job_stage
                            ? this.state.inventoryData
                              .shipment_type_for_shipment.current_job_stage
                            : ""}{" "}
                          of{" "}
                          {this.state.inventoryData &&
                            this.state.inventoryData.shipment_type_for_shipment &&
                            this.state.inventoryData.shipment_type_for_shipment
                              .total_stages
                            ? this.state.inventoryData
                              .shipment_type_for_shipment.total_stages
                            : ""}
                          )
                        </h4>
                      </>
                    )}
                  </div>
                  {/* {(this.state && this.state.isJobComplete === 1) || (this.state && this.state.isFirstStage == "yes") ? "" :
										<>
											{
												(this.state.shipmentStage && this.state.shipmentStage.length > 1)
													?
													<Button
														type='primary'
														className='c-btn c-info c-round'
														icon='eye'
														onClick={() => this.onShowModal()}>
														Force change stage
													</Button>
													: ""}
										</>
									} */}
                </div>
                <div style={{ marginTop: "10px", overflowX: "auto" }}>
                  <Table
                    bordered={true}
                    columns={
                      this.state.isMainAdmin
                        ? columns
                        : this.state.integrationKeyStatus &&
                          this.state.inventoryData.storage_shipment_job_id !==
                          null &&
                          this.state.inventoryData.storage_shipment_job_id !==
                          "" &&
                          this.state.inventoryData.storage_shipment_job_id !==
                          undefined
                          ? columns
                          : columns2
                    }
                    dataSource={this.state.shipmentStage}
                    onChange={this.handleChange}
                    pagination={false}
                    scroll={{ x: 2000 }}
                  />
                </div>
                <div
                  style={{
                    width: "100%",
                    marginTop: "10px",
                    overflowX: "auto",
                    display: "flex",
                    justifyContent: "space-between",
                    flexDirection: "row",
                  }}
                >
                  <h1></h1>

                  <Button
                    type="primary"
                    className="c-btn c-primary c-round"
                    icon="plus"
                    onClick={() =>
                      this.addShipmentTypeStage(
                        this.state.shipmentStage,
                        this.state.inventoryData
                      )
                    }
                  >
                    Add Stage
                  </Button>
                </div>
              </Spin>
            </Spin>
          </div>
        </LayoutContent>
        <Modal
          title="Force Status Change"
          visible={this.state.showModal}
          onOk={this.handleSubmit}
          okText="Change status"
          cancelText="Cancel"
          centered
          maskClosable={false}
          onCancel={this.onCancelModal}
        >
          {this.state.shipmentStage2.length > 0 && (
            <div>
              <p>
                <b>Select stage</b>
              </p>
              <Select
                placeholder="Select stage"
                style={{ width: "100%" }}
                onChange={(e) => this.changeValue(e)}
              >
                {this.state.shipmentStage2.map((result, i) => (
                  <Option value={result.local_shipment_stage_id} key={i}>
                    {result.name}
                  </Option>
                ))}
              </Select>

              <Form.Item label="Reason">
                {getFieldDecorator("reason", {
                  placeholder: "Reason",
                  rules: [
                    {
                      required: true,
                      message: "Please input reason!",
                      whitespace: true,
                    },
                  ],
                })(<Input placeholder="Reason" />)}
              </Form.Item>
            </div>
          )}
        </Modal>
        {
          <Modal
            title="Are You Sure?"
            visible={this.state.deleteModalPDF}
            onOk={this.handleOkPDF}
            okText="Yes"
            cancelText="No"
            centered
            maskClosable={false}
            confirmLoading={this.state.confirmLoading}
            onCancel={this.handleCancelPDF}
          >
            <p>Are you sure you want to delete PDF?</p>
          </Modal>
        }
        {
          <Modal
            title="Are You Sure?"
            visible={this.state.deleteModalShipmentTypeStage}
            onOk={this.handleOkShipmentTypeStage}
            okText="Yes"
            cancelText="No"
            centered
            maskClosable={false}
            confirmLoading={this.state.confirmLoading}
            onCancel={this.handleCancelShipmentTypeStage}
          >
            <p>Are you sure you want to delete shipment type stage?</p>
          </Modal>
        }
        {
          <Modal
            title="Are You Sure?"
            visible={this.state.showModalForAddStage}
            onOk={this.allowToAddNewStage}
            okText="Yes"
            cancelText="No"
            centered
            maskClosable={false}
            confirmLoading={this.state.confirmLoading}
            onCancel={this.allowCancelToAddNewStage}
          >
            <p>
              Adding new stage will change the shipment status from Completed to
              In Progress. Are you sure you want to add new stage?
            </p>
          </Modal>
        }

        {
          <Modal
            title="Are You Sure?"
            visible={this.state.additionalValidationModal}
            centered
            maskClosable={false}
            onCancel={this.handleCancel}
            footer={null}
          >
            <p>{`This action is not allowed as the Current stage is ${this.state.isMakeAddScanCheckedCurrentStage
              ? "Add items to Inventory"
              : "Remove items from Inventory"
              } and the next stage you are trying to replace is Additional Scan.`}</p>
          </Modal>
        }
      </LayoutContentWrapper>
    );
  }
}

export default Form.create()(connect(null, { changeCurrent })(ViewJob));
